{"name": "code-guardian-report", "private": true, "version": "1.4.0", "type": "module", "scripts": {"dev": "vite --host", "prebuild": "echo 'Preparing build...'", "build": "npx vite build", "build:vercel": "chmod +x node_modules/.bin/vite 2>/dev/null || true && npx vite build", "build:production": "NODE_ENV=production npm run build", "preview": "vite preview --host", "type-check": "tsc --noEmit", "start": "npm run dev", "serve": "npm run preview"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.4", "@types/jszip": "^3.4.0", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "jszip": "^3.10.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "recharts": "^2.15.3", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "web-vitals": "^4.2.4"}, "devDependencies": {"@types/node": "^22.5.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.31.0", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "lightningcss": "^1.28.2", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^7.0.5"}}